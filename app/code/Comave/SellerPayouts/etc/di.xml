<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="Webkul\Marketplace\Block\Adminhtml\Orders\Pay"
                type="Comave\SellerPayouts\Rewrite\Webkul\Marketplace\Block\Adminhtml\Orders\Pay"/>

    <virtualType name="SellerPayoutComaveLogger" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">SellerPayoutComaveLogger</argument>
            <argument name="loggerPath" xsi:type="string">MassPayseller</argument>
        </arguments>
    </virtualType>
    <type name="Comave\SellerPayouts\Helper\ConfigProvider">
        <arguments>
            <argument xsi:type="object" name="logger">SellerPayoutComaveLogger</argument>
        </arguments>
    </type>
    <type name="Comave\SellerPayouts\Helper\Data">
        <arguments>
            <argument xsi:type="object" name="logger">SellerPayoutComaveLogger</argument>
        </arguments>
    </type>

    <virtualType name="StripeDetailsComaveLogger" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">StripeDetailsComaveLogger</argument>
            <argument name="loggerPath" xsi:type="string">getCustomerDetails</argument>
        </arguments>
    </virtualType>
    <type name="Comave\SellerPayouts\Block\StripeDetails">
        <arguments>
            <argument xsi:type="object" name="logger">StripeDetailsComaveLogger</argument>
        </arguments>
    </type>

    <virtualType name="PayOutsCronComaveLogger" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">PayOutsCronComaveLogger</argument>
            <argument name="loggerPath" xsi:type="string">Payseller</argument>
        </arguments>
    </virtualType>
    <type name="Comave\SellerPayouts\Cron\PayOuts">
        <arguments>
            <argument xsi:type="object" name="logger">PayOutsCronComaveLogger</argument>
        </arguments>
    </type>

    <virtualType name="CheckAccountsComaveLogger" type="Comave\Logger\Model\ComaveLogger">
        <arguments>
            <argument name="name" xsi:type="string">CheckAccountsComaveLogger</argument>
            <argument name="loggerPath" xsi:type="string">CheckSellerService</argument>
        </arguments>
    </virtualType>
    <type name="Comave\SellerPayouts\Model\Service\AdminNotificationServices">
        <arguments>
            <argument xsi:type="object" name="logger">CheckAccountsComaveLogger</argument>
        </arguments>
    </type>
</config>
