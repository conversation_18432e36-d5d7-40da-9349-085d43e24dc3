<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Model;

use Magento\Framework\Model\AbstractModel;
use Comave\PayoutManagement\Api\Data\PayoutInterface;

class Payout extends AbstractModel implements PayoutInterface
{
    public const CACHE_TAG = 'comave_payout_management';

    protected $_cacheTag = self::CACHE_TAG;

    protected function _construct(): void
    {
        $this->_init(\Comave\PayoutManagement\Model\ResourceModel\Payout::class);
    }

    public function getStripePayoutId(): ?string
    {
        return $this->getData(self::STRIPE_PAYOUT_ID);
    }

    public function setStripePayoutId(string $stripePayoutId): PayoutInterface
    {
        return $this->setData(self::STRIPE_PAYOUT_ID, $stripePayoutId);
    }

    public function getSellerId(): ?int
    {
        $value = $this->getData(self::SELLER_ID);
        return $value ? (int)$value : null;
    }

    public function setSellerId(int $sellerId): PayoutInterface
    {
        return $this->setData(self::SELLER_ID, $sellerId);
    }

    public function getSellerName(): ?string
    {
        return $this->getData(self::SELLER_NAME);
    }

    public function setSellerName(?string $sellerName): PayoutInterface
    {
        return $this->setData(self::SELLER_NAME, $sellerName);
    }

    public function getStripeAccountId(): ?string
    {
        return $this->getData(self::STRIPE_ACCOUNT_ID);
    }

    public function setStripeAccountId(string $stripeAccountId): PayoutInterface
    {
        return $this->setData(self::STRIPE_ACCOUNT_ID, $stripeAccountId);
    }

    public function getAmount(): ?float
    {
        $value = $this->getData(self::AMOUNT);
        return $value ? (float)$value : null;
    }

    public function setAmount(float $amount): PayoutInterface
    {
        return $this->setData(self::AMOUNT, $amount);
    }

    public function getCurrency(): ?string
    {
        return $this->getData(self::CURRENCY);
    }

    public function setCurrency(string $currency): PayoutInterface
    {
        return $this->setData(self::CURRENCY, $currency);
    }

    public function getStatus(): ?string
    {
        return $this->getData(self::STATUS);
    }

    public function setStatus(string $status): PayoutInterface
    {
        return $this->setData(self::STATUS, $status);
    }

    public function getPaymentMethod(): ?string
    {
        return $this->getData(self::PAYMENT_METHOD);
    }

    public function setPaymentMethod(string $paymentMethod): PayoutInterface
    {
        return $this->setData(self::PAYMENT_METHOD, $paymentMethod);
    }

    public function getScheduledDate(): ?string
    {
        return $this->getData(self::SCHEDULED_DATE);
    }

    public function setScheduledDate(?string $scheduledDate): PayoutInterface
    {
        return $this->setData(self::SCHEDULED_DATE, $scheduledDate);
    }

    public function getCompletionDate(): ?string
    {
        return $this->getData(self::COMPLETION_DATE);
    }

    public function setCompletionDate(?string $completionDate): PayoutInterface
    {
        return $this->setData(self::COMPLETION_DATE, $completionDate);
    }

    public function getLastSyncAt(): ?string
    {
        return $this->getData(self::LAST_SYNC_AT);
    }

    public function setLastSyncAt(?string $lastSyncAt): PayoutInterface
    {
        return $this->setData(self::LAST_SYNC_AT, $lastSyncAt);
    }
}
