<?php

/**
 * Copyright © Commercial Avenue. All rights reserved.
 * See COPYING.txt for license details.
 */

declare(strict_types=1);

namespace Comave\PayoutManagement\Ui\DataProvider;

use Magento\Ui\DataProvider\AbstractDataProvider;
use Comave\PayoutManagement\Model\ResourceModel\Payout\CollectionFactory;

class UpcomingPayoutsDataProvider extends AbstractDataProvider
{
    public function __construct(
        $name,
        $primaryFieldName,
        $requestFieldName,
        CollectionFactory $collectionFactory,
        array $meta = [],
        array $data = []
    ) {
        $this->collection = $collectionFactory->create();
        // Always filter for upcoming payouts
        $this->collection->addFieldToFilter('status', ['in' => ['pending', 'in_transit']]);

        parent::__construct($name, $primaryFieldName, $requestFieldName, $meta, $data);
    }

    public function getData(): array
    {
        if (!$this->getCollection()->isLoaded()) {
            $this->getCollection()->load();
        }

        $items = $this->getCollection()->toArray();

        return [
            'totalRecords' => $this->getCollection()->getSize(),
            'items' => array_values($items['items'])
        ];
    }
}
